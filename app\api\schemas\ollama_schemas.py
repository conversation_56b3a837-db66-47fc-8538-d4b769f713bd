"""
Ollama API format schemas.

This module contains Pydantic models that represent the Ollama API format
for chat requests and responses, following the official Ollama API specification.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Literal

from pydantic import BaseModel, Field


class OllamaMessage(BaseModel):
    """
    Represents a message in Ollama chat format.
    
    Supports system, user, and assistant roles with optional image content.
    """
    
    role: Literal["system", "user", "assistant"] = Field(
        ...,
        description="The role of the message sender"
    )
    
    content: str = Field(
        ...,
        description="The text content of the message"
    )
    
    images: Optional[List[str]] = Field(
        None,
        description="Optional list of base64-encoded images"
    )


class OllamaChatRequest(BaseModel):
    """
    Represents an Ollama chat completion request.
    
    This matches the format expected by Ollama's /api/chat endpoint.
    """
    
    model: str = Field(
        ...,
        description="The model name to use for the chat completion"
    )
    
    messages: List[OllamaMessage] = Field(
        ...,
        description="List of messages in the conversation"
    )
    
    stream: Optional[bool] = Field(
        False,
        description="Whether to stream the response"
    )
    
    format: Optional[str] = Field(
        None,
        description="Response format (e.g., 'json')"
    )
    
    options: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional model options"
    )
    
    template: Optional[str] = Field(
        None,
        description="Custom prompt template"
    )
    
    system: Optional[str] = Field(
        None,
        description="System message to use"
    )
    
    raw: Optional[bool] = Field(
        None,
        description="Whether to use raw mode"
    )
    
    keep_alive: Optional[str] = Field(
        None,
        description="How long to keep the model loaded"
    )


class OllamaResponseMessage(BaseModel):
    """
    Represents a message in an Ollama response.
    """
    
    role: Literal["assistant"] = Field(
        "assistant",
        description="The role of the response message"
    )
    
    content: str = Field(
        ...,
        description="The generated response content"
    )


class OllamaChatResponse(BaseModel):
    """
    Represents an Ollama chat completion response.
    
    This matches the format returned by Ollama's /api/chat endpoint
    for non-streaming responses.
    """
    
    model: str = Field(
        ...,
        description="The model that generated the response"
    )
    
    created_at: datetime = Field(
        ...,
        description="Timestamp when the response was created"
    )
    
    message: OllamaResponseMessage = Field(
        ...,
        description="The generated message"
    )
    
    done: bool = Field(
        True,
        description="Whether the response is complete"
    )
    
    total_duration: Optional[int] = Field(
        None,
        description="Total time taken in nanoseconds"
    )
    
    load_duration: Optional[int] = Field(
        None,
        description="Time taken to load the model in nanoseconds"
    )
    
    prompt_eval_count: Optional[int] = Field(
        None,
        description="Number of tokens in the prompt"
    )
    
    prompt_eval_duration: Optional[int] = Field(
        None,
        description="Time taken to evaluate the prompt in nanoseconds"
    )
    
    eval_count: Optional[int] = Field(
        None,
        description="Number of tokens in the response"
    )
    
    eval_duration: Optional[int] = Field(
        None,
        description="Time taken to generate the response in nanoseconds"
    )


class OllamaStreamResponse(BaseModel):
    """
    Represents a single chunk in an Ollama streaming response.
    
    Used for streaming chat completions.
    """
    
    model: str = Field(
        ...,
        description="The model generating the response"
    )
    
    created_at: datetime = Field(
        ...,
        description="Timestamp when the chunk was created"
    )
    
    message: OllamaResponseMessage = Field(
        ...,
        description="The message chunk"
    )
    
    done: bool = Field(
        ...,
        description="Whether this is the final chunk"
    )
    
    # Metadata fields (only present in final chunk when done=true)
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    prompt_eval_duration: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None


class OllamaErrorResponse(BaseModel):
    """
    Represents an error response from Ollama.
    """

    error: str = Field(
        ...,
        description="Error message"
    )


class OllamaModelDetails(BaseModel):
    """
    Represents detailed model information in Ollama format.

    This matches the details field in Ollama's show response.
    """

    parent_model: str = Field(
        "",
        description="Parent model name"
    )

    format: str = Field(
        "gguf",
        description="Model format"
    )

    family: str = Field(
        "llama",
        description="Model family"
    )

    families: List[str] = Field(
        default_factory=lambda: ["llama"],
        description="List of model families"
    )

    parameter_size: str = Field(
        "unknown",
        description="Parameter size (e.g., '7B', '13B')"
    )

    quantization_level: str = Field(
        "unknown",
        description="Quantization level (e.g., 'Q4_0', 'Q8_0')"
    )


class OllamaModel(BaseModel):
    """
    Represents a model in the Ollama model list format.

    This matches the format expected by Ollama's /api/tags endpoint
    for individual model objects.
    """

    name: str = Field(
        ...,
        description="The model name"
    )

    modified_at: datetime = Field(
        ...,
        description="When the model was last modified"
    )

    size: int = Field(
        ...,
        description="Size of the model in bytes"
    )

    digest: str = Field(
        ...,
        description="SHA256 digest of the model"
    )

    details: Optional[OllamaModelDetails] = Field(
        None,
        description="Detailed model information"
    )

    class Config:
        """Pydantic configuration for proper JSON serialization."""
        json_encoders = {
            datetime: lambda v: v.isoformat() + 'Z' if v.tzinfo is None else v.isoformat()
        }


class OllamaTagsResponse(BaseModel):
    """
    Represents a model list response in Ollama format.

    This matches the format returned by Ollama's /api/tags endpoint.
    """

    models: List[OllamaModel] = Field(
        ...,
        description="List of available models"
    )


class OllamaShowRequest(BaseModel):
    """
    Represents a request to Ollama's /api/show endpoint.

    Used to get detailed information about a specific model.
    """

    model: str = Field(
        ...,
        description="The name of the model to show information for"
    )

    verbose: Optional[bool] = Field(
        False,
        description="Whether to return verbose information"
    )


class OllamaShowResponse(BaseModel):
    """
    Represents the response from Ollama's /api/show endpoint.

    Contains detailed information about a specific model.
    """

    modelfile: str = Field(
        "",
        description="The modelfile content for this model"
    )

    parameters: str = Field(
        "",
        description="Model parameters as a string"
    )

    template: str = Field(
        "",
        description="The prompt template for this model"
    )

    details: OllamaModelDetails = Field(
        ...,
        description="Detailed model information"
    )

    model_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional model information"
    )

    capabilities: List[str] = Field(
        default_factory=lambda: ["completion"],
        description="List of model capabilities"
    )
